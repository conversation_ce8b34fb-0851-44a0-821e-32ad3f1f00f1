<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电源网</title>
  <!-- 第三方css -->
  <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
  <link rel="stylesheet" href="../../modules/layui/css/layui.css">
  <!-- 第三方库js -->
  <script src="../../modules/jq.js"></script>
  <script src="../../modules/layui/layui.js"></script>
  <script src="../../modules/xm-select/xm-select.js"></script>
  <!-- 基础css -->
  <link rel="stylesheet" href="../../css/public.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/footer.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/media.css">
  <!-- <link rel="stylesheet" href="../../css/mySidebar.css"> -->

  <!-- 本页私有css -->
  <link rel="stylesheet" href="../../css/pageCss/intelligent.css">
  <!-- 综合-script -->
  <script src="../../script/index.js" defer></script>
  <!-- 产品分类模块 -->
  <script src="../../script/productCategory.js" defer></script>
  <!-- 所有页面组成部分 -->
  <script src="../../components/common.js" defer></script>
  <script src="../../components/mySidebar.js" defer></script>
  <!-- <script src="../../components/myTable.js" defer></script> -->
  <!-- 单页面组成部分 -->
  <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
  <!-- 引入头部组件 -->
  <div id="header"></div>

  <div class="intelligent">
    <div class="intelligent-left">
      <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title">
          <li class="layui-this">
            <img src="../../images/power/zhinengxuanxing_gaoliang.png" alt="">
            <span>智能选型</span>
          </li>
          <li>
            <img src="../../images/power/rengong_moren.png" alt="">
            <span>人工选型</span>
          </li>
        </ul>
        <div class="layui-tab-content">
          <!-- 智能选型 -->
          <div class="layui-tab-item layui-show mind">
            <p>如无法找到合适产品或需替代，可选择人工选型</p>
            <div class="type"> <img src="../../images/power/leixing.png" alt=""><span>产品类型</span></div>

            <div class="layui-tab layui-tab-brief " lay-filter="demoTabFilter">


              <ul class="layui-tab-title modules" id="demoTabsHeader">
                <li class="layui-this">AC/DC电源模块</li>
                <li>DC/DC电源模块</li>
                <li>其他模块其他模块其他模块 </li>
              </ul>
              <div class="layui-tab-content" id="demoTabsBody">
                <!-- AC/DC电源模块 -->
                <div class="layui-tab-item layui-show dcdc1">
                  <form class="layui-form">
                    <div class="demo-dcdc1-container container">
                      <div class="layui-form-item">
                        <label class="layui-form-label">输出功率:</label>
                        <div class="layui-input-block">
                          <input type="text" name="output" value="1" placeholder="请填写(1~75)" lay-reqtext="请填写用户名"
                            autocomplete="off" class="layui-input">
                          <div class="layui-input-split layui-input-suffix">
                            W
                          </div>
                        </div>

                      </div>
                      <div class="layui-form-item inp">
                        <label class="layui-form-label">输入电压:</label>
                      </div>
                      <div class="layui-col-md5">
                        <div class="layui-form-item inp">
                          <div class="layui-input-block">
                            <input type="text" name="min" value="2" placeholder="最小" class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC
                            </div>

                          </div>

                        </div>

                      </div>
                      <div class="layui-form-mid layui-text-em">
                        <i class="layui-icon layui-icon-reduce-circle"></i>
                      </div>
                      <div class="layui-col-md5">
                        <div class="layui-form-item inp">
                          <div class="layui-input-block">
                            <input type="text" name="max" value="3" placeholder="最大" class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC
                            </div>
                          </div>
                        </div>
                      </div>


                      <div class="layui-col-md12">
                        <div class="layui-form-item">
                          <label class="layui-form-label Vout">输出电压:&nbsp;&nbsp; Vout1: </label>
                          <div class="layui-input-inline">
                            <input type="text" name="Vout1" value="4" lay-verify="required" placeholder="单路输出"
                              class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC <i class="layui-icon layui-icon-add-circle-fine"></i>
                            </div>
                          </div>
                        </div>
                      </div>


                      <div class="layui-col-md12 tip">如需多路输出产品请点击+号图标</div>


                      <div class="layui-form-item inp">
                        <label class="layui-form-label">隔离电压:</label>
                      </div>

                      <ul class="pics">
                        <li>
                          <span data-value="a">非隔离 </span>
                        </li>
                        <li>
                          <span data-value="c">1500VDC</span>
                        </li>
                        <li>
                          <span data-value="e">3000VDC</span>
                        </li>
                        <li>
                          <span data-value="b">6000VDC</span>
                        </li>
                      </ul>


                      <div class="layui-form-item inp">

                        <label class="layui-form-label">封装类型:</label>
                      </div>
                      <ul class="pics">
                        <li>
                          <span data-value="DIP">DIP</span>
                        </li>
                        <li>
                          <span data-value="SIP">SIP</span>
                        </li>
                      </ul>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">产品特点:</label>
                      </div>

                      <ul class="pics">
                        <li>
                          <span data-value="a">工业通用</span>
                        </li>
                        <li>
                          <span data-value="c">光伏适用</span>
                        </li>
                        <li>
                          <span data-value="e">经济开板</span>
                        </li>
                        <li>
                          <span data-value="b">汽车专用</span>
                        </li>
                        <li>
                          <span data-value="f">高隔离(电力/医疗)</span>
                        </li>
                        <li>
                          <span data-value="d">铁路适用</span>
                        </li>
                      </ul>


                      <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="dcdcSubmit1">点击筛选</button>
                        <button type="reset" class="layui-btn layui-btn-fluid" lay-filter="dcdcReset1">重置</button>
                      </div>

                    </div>
                  </form>
                </div>
                
                 <!-- DC/DC电源模块 -->
                <div class="layui-tab-item dcdc2">
                  <form class="layui-form">
                    <div class="demo-dcdc2-container container">
                      <div class="layui-form-item">
                        <label class="layui-form-label">输出功率:</label>
                        <div class="layui-input-block">
                          <input type="text" name="output" value="1" placeholder="请填写(1~75)" lay-reqtext="请填写用户名"
                            autocomplete="off" class="layui-input">
                          <div class="layui-input-split layui-input-suffix">
                            W
                          </div>
                        </div>
                      </div>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">输入电压:</label>
                      </div>
                      <div class="layui-col-md5">
                        <div class="layui-form-item inp">
                          <div class="layui-input-block">
                            <input type="text" name="min" value="2" placeholder="最小" class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="layui-form-mid layui-text-em">
                        <i class="layui-icon layui-icon-reduce-circle"></i>
                      </div>
                      <div class="layui-col-md5">
                        <div class="layui-form-item inp">
                          <div class="layui-input-block">
                            <input type="text" name="max" value="3" placeholder="最大" class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="layui-col-md12">
                        <div class="layui-form-item">
                          <label class="layui-form-label Vout">输出电压:&nbsp;&nbsp; Vout1: </label>
                          <div class="layui-input-inline">
                            <input type="text" name="Vout1" value="4" lay-verify="required" placeholder="单路输出"
                              class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC <i class="layui-icon layui-icon-add-circle-fine"></i>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="layui-col-md12 tip">如需多路输出产品请点击+号图标</div>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">隔离电压:</label>
                      </div>

                      <ul class="pics">
                        <li>
                          <span data-value="a">非隔离 </span>
                        </li>
                        <li>
                          <span data-value="c">1500VDC</span>
                        </li>
                        <li>
                          <span data-value="e">3000VDC</span>
                        </li>
                        <li>
                          <span data-value="b">6000VDC</span>
                        </li>
                      </ul>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">封装类型:</label>
                      </div>
                      <ul class="pics">
                        <li>
                          <span data-value="DIP">DIP</span>
                        </li>
                        <li>
                          <span data-value="SIP">SIP</span>
                        </li>
                      </ul>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">产品特点:</label>
                      </div>

                      <ul class="pics">
                        <li>
                          <span data-value="a">工业通用</span>
                        </li>
                        <li>
                          <span data-value="c">光伏适用</span>
                        </li>
                        <li>
                          <span data-value="e">经济开板</span>
                        </li>
                        <li>
                          <span data-value="b">汽车专用</span>
                        </li>
                        <li>
                          <span data-value="f">高隔离(电力/医疗)</span>
                        </li>
                        <li>
                          <span data-value="d">铁路适用</span>
                        </li>
                      </ul>

                      <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="dcdcSubmit2">点击筛选</button>
                        <button type="reset" class="layui-btn layui-btn-fluid" lay-filter="dcdcReset2">重置</button>
                      </div>
                    </div>
                  </form>
                </div>

                <!-- 其他模块 -->
                <div class="layui-tab-item dcdc3">
                  <form class="layui-form">
                    <div class="demo-dcdc3-container container">
                      <div class="layui-form-item">
                        <label class="layui-form-label">输出功率:</label>
                        <div class="layui-input-block">
                          <input type="text" name="output" value="1" placeholder="请填写(1~75)" lay-reqtext="请填写用户名"
                            autocomplete="off" class="layui-input">
                          <div class="layui-input-split layui-input-suffix">
                            W
                          </div>
                        </div>
                      </div>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">输入电压:</label>
                      </div>
                      <div class="layui-col-md5">
                        <div class="layui-form-item inp">
                          <div class="layui-input-block">
                            <input type="text" name="min" value="2" placeholder="最小" class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="layui-form-mid layui-text-em">
                        <i class="layui-icon layui-icon-reduce-circle"></i>
                      </div>
                      <div class="layui-col-md5">
                        <div class="layui-form-item inp">
                          <div class="layui-input-block">
                            <input type="text" name="max" value="3" placeholder="最大" class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="layui-col-md12">
                        <div class="layui-form-item">
                          <label class="layui-form-label Vout">输出电压:&nbsp;&nbsp; Vout1: </label>
                          <div class="layui-input-inline">
                            <input type="text" name="Vout1" value="4" lay-verify="required" placeholder="单路输出"
                              class="layui-input">
                            <div class="layui-input-split layui-input-suffix">
                              VDC <i class="layui-icon layui-icon-add-circle-fine"></i>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="layui-col-md12 tip">如需多路输出产品请点击+号图标</div>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">隔离电压:</label>
                      </div>

                      <ul class="pics">
                        <li>
                          <span data-value="a">非隔离 </span>
                        </li>
                        <li>
                          <span data-value="c">1500VDC</span>
                        </li>
                        <li>
                          <span data-value="e">3000VDC</span>
                        </li>
                        <li>
                          <span data-value="b">6000VDC</span>
                        </li>
                      </ul>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">封装类型:</label>
                      </div>
                      <ul class="pics">
                        <li>
                          <span data-value="DIP">DIP</span>
                        </li>
                        <li>
                          <span data-value="SIP">SIP</span>
                        </li>
                      </ul>

                      <div class="layui-form-item inp">
                        <label class="layui-form-label">产品特点:</label>
                      </div>

                      <ul class="pics">
                        <li>
                          <span data-value="a">工业通用</span>
                        </li>
                        <li>
                          <span data-value="c">光伏适用</span>
                        </li>
                        <li>
                          <span data-value="e">经济开板</span>
                        </li>
                        <li>
                          <span data-value="b">汽车专用</span>
                        </li>
                        <li>
                          <span data-value="f">高隔离(电力/医疗)</span>
                        </li>
                        <li>
                          <span data-value="d">铁路适用</span>
                        </li>
                      </ul>

                      <div class="layui-form-item">
                        <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="dcdcSubmit3">点击筛选</button>
                        <button type="reset" class="layui-btn layui-btn-fluid" lay-filter="dcdcReset3">重置</button>
                      </div>
                    </div>
                  </form>
                </div>


              </div>
            </div>

          </div>
          <!-- 人工选型 -->
          <div class="layui-tab-item manual">
            <p>请填写下方内容，我可将第一时间与您电话沟通</p>
            <form class="layui-form">
              <div class="demo-manual-container">
                <div class="layui-form-item">
                  <label class="layui-form-label">输出功率:</label>
                  <div class="layui-input-block first">
                    <input type="text" lay-verify="required" name="output" value="1" placeholder="请填写(1~75)"
                      lay-reqtext="请填写输出功率" autocomplete="off" class="layui-input my-input">
                    <div class="layui-input-split layui-input-suffix">
                      W
                    </div>
                  </div>

                </div>
                <div class="layui-form-item inp">
                  <label class="layui-form-label">输入电压:</label>
                </div>
                <div class="layui-col-md5">
                  <div class="layui-form-item inp">
                    <div class="layui-input-block">
                      <input type="text" lay-verify="required" name="min" value="2" placeholder="最小"
                        class="layui-input my-input">
                      <div class="layui-input-split layui-input-suffix">
                        VDC
                      </div>

                    </div>

                  </div>

                </div>
                <div class="layui-form-mid layui-text-em">
                  <i class="layui-icon layui-icon-reduce-circle"></i>
                </div>
                <div class="layui-col-md5">
                  <div class="layui-form-item inp">
                    <div class="layui-input-block">
                      <input type="text" lay-verify="required" name="max" value="3" placeholder="最大"
                        class="layui-input my-input">
                      <div class="layui-input-split layui-input-suffix">
                        VDC
                      </div>
                    </div>
                  </div>
                </div>


                <div class="layui-col-md12">
                  <div class="layui-form-item ">
                    <label class="layui-form-label Vout">输出电压:&nbsp;&nbsp; Vout1: </label>
                    <div class="layui-input-inline ">
                      <input type="text" name="Vout1" value="4" lay-verify="required" placeholder="单路输出"
                        class="layui-input my-input">
                      <div class="layui-input-split layui-input-suffix">
                        VDC <i class="layui-icon layui-icon-add-circle-fine"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="layui-col-md12 tip">如需多路输出产品请点击+号图标</div>
                <div class="layui-col-md11">
                  <div class="use">
                    使用过品牌&型号:
                  </div>
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <input type="text" name="brandModel" lay-verify="required" placeholder="请输入" autocomplete="off"
                        class="layui-input my-Input" value="5">
                    </div>
                  </div>
                </div>
                <div class="layui-form-item inp">
                  <label class="layui-form-label">产品需求:</label>
                </div>
                <div class="layui-col-md11">
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <input type="text" name="demand" lay-verify="required" placeholder="请输入" autocomplete="off"
                        class="layui-input" value="6">
                    </div>
                  </div>
                </div>
                <div class="layui-form-item inp">
                  <label class="layui-form-label">您的称呼:</label>
                </div>
                <div class="layui-col-md11">
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <input type="text" name="call" lay-verify="required" placeholder="请输入" autocomplete="off"
                        class="layui-input" value="7">
                    </div>
                  </div>
                </div>
                <div class="layui-form-item inp">
                  <label class="layui-form-label">您的电话:</label>
                </div>
                <div class="layui-col-md11">
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <input type="text" name="phone" lay-verify="required" placeholder="请输入" autocomplete="off"
                        class="layui-input" value="8">
                    </div>
                  </div>
                </div>
                <div class="layui-form-item inp bottom">
                  <label class="myLabel">您的公司全称:</label>
                </div>
                <div class="layui-col-md11">
                  <div class="layui-form-item">
                    <div class="layui-input-block">
                      <input type="text" name="fullName" lay-verify="required" placeholder="请输入" autocomplete="off"
                        class="layui-input" value="9">
                    </div>
                  </div>
                </div>

                <div class="layui-form-item inp bottom">
                  <label class="myLabel">所在区域:</label>
                </div>
                <div class="layui-input-inline last">
                  <div class="layui-form-item">
                    <div class="layui-col-md5">
                      <select name="quiz1" lay-verify="required">
                        <option value="" selected>请选择省</option>
                        <option value="浙江">浙江省</option>
                        <option value="你的工号">江西省</option>
                        <option value="你最喜欢的老师">福建省</option>
                      </select>
                    </div>
                    <div class="layui-form-mid layui-text-em"></div>
                    <div class="layui-col-md5">
                      <select name="quiz2" lay-verify="required">
                        <option value="">请选择市</option>
                        <option value="杭州">杭州</option>
                        <option value="宁波" disabled>宁波</option>
                        <option value="温州">温州</option>
                        <option value="温州">台州</option>
                        <option value="温州">绍兴</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="layui-form-item">
                  <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="manualSubmit">提交选型信息</button>
                </div>
              </div>
            </form>
          </div>


        </div>
      </div>
    </div>
    <!-- 表格 -->
    <!-- <div class="intelligent-right">
      <div class="tableBox">
        <table class="layui-table">
          <thead>
            <tr>
              <th>产品型号</th>
              <th>产品图片</th>
              <th>功率 <br> (W)</th>
              <th>输入电压<br>(VAC)</th>
              <th>输入电压<br>(VDC)</th>
              <th>输出电压<br>(VDC)</th>
              <th>输出电流<br>(mA)</th>
              <th>输出路数<br>(mA)</th>
              <th>隔离电压<br>(mA)</th>
              <th>封装形式</th>
              <th>封装尺寸<br>(mm)</th>
              <th class="long">资料下载<br>(3D/PCB封装库/<br>原理图库)</th>
              <th>认证/标准</th>
              <th>技术<br>手册</th>
              <th>样品<br>申请</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <div onclick="toRouter(this)" data-link="../product/productSeries.html">
                  2W系列
                </div>
              </td>
              <td>
                <img class="product-img" src="../../images/power/3w5.png" alt="">
              </td>
              <td>2W</td>
              <td>90-265</td>
              <td>120-350</td>
              <td>3.3V</td>
              <td>600mA</td>
              <td>-</td>
              <td>3000VAC</td>
              <td>SIP</td>
              <td>30.7*16.5*10</td>
              <td><img src="../../images/power/xiazai.png" alt=""></td>
              <td class="renzhengBox">
                <img src="../../images/power/renzheng.png" alt="">
              </td>
              <td><img src="../../images/power/pdf.png" alt=""></a></td>
              <td><img src="../../images/power/shenqing.png" alt=""></td>
            </tr>

          </tbody>
        </table>

      </div>

    </div> -->

  </div>
  <!-- <div class="paging">
    <span class="warn">上方产品参数仅供参考，详细技术参数请以技术规格书为准</span>
    <div id="demo-laypage-all"></div>
  </div> -->

  <div class="bug"></div>

  <!-- 引入底部组件 -->
  <div id="footer"></div>
</body>
<script>
  layui.use(['element'], function (element) {
    // Tab 切换事件，监听 lay-filter="demoTabFilter" 的 Tab
    element.on('tab(demoTabFilter)', function (data) {
      // console.log(this); // 当前 Tab 标题的 DOM 对象
      // console.log(data.index); // 得到当前 Tab 的所在下标
      // console.log(data.elem); // 得到 Tab 的容器 DOM 对象
    });
  });

  document.addEventListener('DOMContentLoaded', function () {
    layui.use(['form','element', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var laydate = layui.laydate;
      var util = layui.util;
      var element = layui.element;

      //点击添加多路输出
      document.addEventListener('click', function (e) {

        if (e.target.classList.contains('layui-icon-add-circle-fine')) {
          // 判断是哪个表单的添加按钮
          const isDCDC1 = e.target.closest('.dcdc1') !== null;
          const isDCDC2 = e.target.closest('.dcdc2') !== null;
          const isDCDC3 = e.target.closest('.dcdc3') !== null;
          const isManual = e.target.closest('.manual') !== null;

          if (isDCDC1) {
            addNewOutputField('dcdc1');
          } else if (isDCDC2) {
            addNewOutputField('dcdc2');
          } else if (isDCDC3) {
            addNewOutputField('dcdc3');
          } else if (isManual) {
            addNewOutputField('manual');
          }
          // console.log('isDCDC1:', isDCDC1, 'isDCDC2:', isDCDC2, 'isDCDC3:', isDCDC3, 'isManual:', isManual);
        }
      });

      // 添加新的输出字段
      function addNewOutputField(type) {
        console.log('正在添加输出字段，类型:', type);
        let containerSelector;
        if (type === 'manual') {
          containerSelector = '.demo-manual-container';
        } else {
          containerSelector = `.demo-${type}-container`;
        }
        const outputContainer = document.querySelector(containerSelector);
        if (!outputContainer) {
          console.error(`找不到容器: ${containerSelector}`);
          return;
        }

        // 获取所有现有的Vout字段（排除标题行）
        const outputFields = document.querySelectorAll(`.${type} .layui-form-item .Vout`);
        const existingVoutNumbers = [];

        // 收集现有的Vout编号
        for (let i = 0; i < outputFields.length; i++) {
          const labelText = outputFields[i].textContent;
          const match = labelText.match(/Vout(\d+):/);
          if (match) {
            const voutNumber = parseInt(match[1]);
            existingVoutNumbers.push(voutNumber);
          }
        }

        // 限制最多4路输出（Vout1到Vout4）
        if (existingVoutNumbers.length >= 4) {
          layer.msg('最多支持4路输出');
          return;
        }

        // 找到下一个可用的Vout编号（1-4范围内）
        let newOutputNumber = 1;
        for (let i = 1; i <= 4; i++) {
          if (!existingVoutNumbers.includes(i)) {
            newOutputNumber = i;
            break;
          }
        }

        // 如果编号超出范围，不允许添加
        if (newOutputNumber > 4) {
          layer.msg('Vout编号超出范围(1-4)');
          return;
        }

        // 根据Vout编号设置对应的placeholder
        let placeholderText = '';
        switch (newOutputNumber) {
          case 1:
            placeholderText = '单路输出';
            break;
          case 2:
            placeholderText = '两路输出';
            break;
          case 3:
            placeholderText = '三路输出';
            break;
          case 4:
            placeholderText = '四路输出';
            break;
          default:
            placeholderText = '多路输出';
        }

        const newOutputField = document.createElement('div');
        newOutputField.className = 'layui-col-md12';
        newOutputField.innerHTML = `
          <div class="layui-form-item">
            <label class="layui-form-label Vout">Vout${newOutputNumber}: </label>
            <div class="layui-input-inline ">
              <input type="text" name="Vout${newOutputNumber}" value="" lay-verify="required" placeholder="${placeholderText}" class="layui-input my-input">
              <div class="layui-input-split layui-input-suffix">
                VDC <i class="layui-icon layui-icon-reduce-circle del"></i>
              </div>
            </div>
          </div>
        `;

        // 在提示文字之前插入新的输出字段
        const tipElement = outputContainer.querySelector('.tip');
        if (tipElement) {
          outputContainer.insertBefore(newOutputField, tipElement);
        } else {
          outputContainer.appendChild(newOutputField);
        }

        // 重新渲染表单
        form.render();
      }

      // 删除输出字段
      document.addEventListener('click', function (e) {
        if (e.target.classList.contains('layui-icon-reduce-circle')) {
          const outputItem = e.target.closest('.layui-col-md12');
          if (outputItem) {
            // 检查是否包含"输出电压:"文本，如果包含则说明是标题行，不允许删除
            const labelText = outputItem.querySelector('.Vout').textContent;
            if (labelText.includes('输出电压:')) {
              return;
            }

            // 检查是否是Vout1，如果是则不允许删除
            const match = labelText.match(/Vout(\d+):/);
            if (match && parseInt(match[1]) === 1) {
              layer.msg('Vout1是固定字段，不能删除');
              return;
            }

            // 直接删除字段，不重新编号
            outputItem.remove();

            // 重新渲染表单
            form.render();
          }
        }
      });

      // 重新编号输出字段（已废弃，保持原有编号）
      function renumberOutputFields(type) {
        // 此函数已废弃，现在保持原有的Vout编号不变
        // Vout字段编号固定在1-4范围内，删除后不重新编号
        console.log('重新编号功能已禁用，保持原有Vout编号');
      }


      //ACDC 多选
      //DCDC 隔离电压 封装类型 产品特点 多选
      //其他
      let arr1 = []
      let arr2 = []
      let arr3 = []

      // 通用的多选处理函数
      function handleMultipleSelection(container, selector, targetArray) {
        $(container).on('click', selector, function (e) {
          e.stopPropagation();
          const clickedItem = $(this);
          clickedItem.toggleClass('act');

          // 更新数组
          targetArray.length = 0; // 清空数组
          $(container).find(selector + '.act').each(function () {
            targetArray.push($(this).find('span').data('value'));
          });

          // console.log('选中的值:', targetArray);
        });
      }

      const multiSelectModules = ['.dcdc1', '.dcdc2', '.dcdc3'];
      const selectors = [
        '.pics:nth-of-type(1) li', // 隔离电压
        '.pics:nth-of-type(2) li', // 封装类型
        '.pics:nth-of-type(3) li'  // 产品特点
      ];
      const arrs = [arr1, arr2, arr3];

      multiSelectModules.forEach(moduleClass => {
        selectors.forEach((selector, idx) => {
          handleMultipleSelection(moduleClass, selector, arrs[idx]);
        });
      });

      // 模块配置
      const moduleConfig = {
        dcdc1: {
          selector: '.dcdc1',
          filter: 'dcdcSubmit1',
          resetFilter: 'dcdcReset1',
          lists: [arr1, arr2, arr3]
        },
        dcdc2: {
          selector: '.dcdc2',
          filter: 'dcdcSubmit2',
          resetFilter: 'dcdcReset2',
          lists: [arr1, arr2, arr3]
        },
        dcdc3: {
          selector: '.dcdc3',
          filter: 'dcdcSubmit3',
          resetFilter: 'dcdcReset3',
          lists: [arr1, arr2, arr3]
        }
      };

      // 通用的表单提交处理函数
      function handleFormSubmit(type) {
        form.on(`submit(${moduleConfig[type].filter})`, function(data) {
          const field = data.field;
          // 添加多选值到表单数据
          moduleConfig[type].lists.forEach((list, index) => {
            field[`arr${index + 1}`] = list;
          });
          console.log('field :>> ', field);
          return false;
        });
      }

      // 通用的表单重置处理函数
      function handleFormReset(type) {
        $(`.layui-btn[lay-filter="${moduleConfig[type].resetFilter}"]`).on('click', function(e) {
          e.preventDefault();
          resetFormContent(type);
          return false;
        });
      }

      // 重置表单内容的通用函数
      function resetFormContent(type) {
        // 重置表单
        $(`${moduleConfig[type].selector} form`)[0].reset();
        
        // 重置所有多选
        $(`${moduleConfig[type].selector} .pics li`).removeClass('act');
        moduleConfig[type].lists.forEach(list => list.length = 0);
        
        // 重置多路输出字段
        const outputFields = document.querySelectorAll(`${moduleConfig[type].selector} .layui-form-item .Vout`);
        for (let i = outputFields.length - 1; i > 0; i--) {
          outputFields[i].closest('.layui-col-md12').remove();
        }
        
        // 重新渲染表单
        form.render();
      }

      // 初始化所有模块的表单处理
      Object.keys(moduleConfig).forEach(type => {
        console.log('type :>> ', type);
        handleFormSubmit(type);
        handleFormReset(type);
      });

      // 监听 tab 切换事件
      element.on('tab(demoTabFilter)', function(data) {
        // 获取当前 tab 索引
        const currentIndex = data.index;
        // console.log('currentIndex :>> ', currentIndex);
        
        // 重置所有其他 tab 的内容
        Object.keys(moduleConfig).forEach((type, index) => {
          if (index !== currentIndex) {
            resetFormContent(type);
          }
        });
      });

      // 监听顶层 tab 切换事件（智能选型和人工选型）
      element.on('tab()', function(data) {
        // 获取当前 tab 索引
        const currentIndex = data.index;
        // console.log('top tab currentIndex :>> ', currentIndex);
        
        if (currentIndex === 0) { // 切换到智能选型
          // 重置人工选型表单
          resetManualForm();
        } else if (currentIndex === 1) { // 切换到人工选型
          // 重置所有智能选型表单
          Object.keys(moduleConfig).forEach(type => {
            resetFormContent(type);
          });
        }
      });

      // 提交事件
      form.on('submit(manualSubmit)', function (data) {
        var field = data.field;
        console.log('field :>> ', field);
        resetManualForm();
        return false;
      });

      // 重置manual表单
      function resetManualForm() {
        $('.manual form')[0].reset();
        const outputFields = document.querySelectorAll('.manual .layui-form-item .Vout');
        for (let i = outputFields.length - 1; i > 0; i--) {
          outputFields[i].closest('.layui-col-md12').remove();
        }
        $('.manual select').val('');
        form.render();
      }

    });
  });
  // 在表格加载完成后初始化分页器
  layui.use(['laypage'], function () {
    var laypage = layui.laypage;
    // 完整显示
    laypage.render({
      elem: 'demo-laypage-all', // 元素 id
      count: 100, // 数据总数
      layout: ['page', 'limit', 'skip'], // 功能布局
      jump: function (obj) {
        // console.log(obj);
      }
    });
  });


</script>

</html>