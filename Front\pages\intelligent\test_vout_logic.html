<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vout字段动态添加逻辑测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .vout-field {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .vout-label {
            min-width: 100px;
            font-weight: bold;
            color: #495057;
        }
        .vout-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 0 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        .btn-add {
            background-color: #28a745;
            color: white;
        }
        .btn-remove {
            background-color: #dc3545;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">Vout字段动态添加逻辑测试</h2>
        
        <div class="info">
            <strong>测试规则：</strong>
            <ul>
                <li>Vout字段编号范围：1-4</li>
                <li>Vout1是固定的，不能删除</li>
                <li>最多支持4路输出</li>
                <li>删除后不重新编号，保持原有编号</li>
                <li>动态添加时按可用编号分配</li>
                <li><strong>新规则：Vout编号对应placeholder - Vout1=单路输出，Vout2=两路输出，Vout3=三路输出，Vout4=四路输出</strong></li>
            </ul>
        </div>

        <div id="vout-container">
            <div class="vout-field" data-vout="1">
                <span class="vout-label">Vout1:</span>
                <input type="text" class="vout-input" placeholder="单路输出" value="">
                <button class="btn btn-add" onclick="addVoutField()">添加</button>
                <button class="btn btn-remove" onclick="removeVoutField(1)" disabled>删除</button>
            </div>
        </div>

        <div style="margin-top: 20px;">
            <button class="btn btn-add" onclick="addVoutField()">+ 添加Vout字段</button>
            <button class="btn" onclick="resetFields()" style="background-color: #6c757d; color: white;">重置</button>
        </div>

        <div style="margin-top: 20px;">
            <h4>操作日志：</h4>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // 模拟修改后的逻辑
        let existingVoutNumbers = [1]; // 初始只有Vout1

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updatePlaceholder() {
            // 更新每个输入框的placeholder，根据其Vout编号
            document.querySelectorAll('.vout-field').forEach(field => {
                const voutNumber = parseInt(field.getAttribute('data-vout'));
                const input = field.querySelector('.vout-input');

                let placeholderText = '';
                switch (voutNumber) {
                    case 1:
                        placeholderText = '单路输出';
                        break;
                    case 2:
                        placeholderText = '两路输出';
                        break;
                    case 3:
                        placeholderText = '三路输出';
                        break;
                    case 4:
                        placeholderText = '四路输出';
                        break;
                    default:
                        placeholderText = '多路输出';
                }

                input.placeholder = placeholderText;
            });
        }

        function addVoutField() {
            log('尝试添加新的Vout字段...');
            
            // 限制最多4路输出
            if (existingVoutNumbers.length >= 4) {
                alert('最多支持4路输出');
                log('❌ 添加失败：已达到最大输出路数(4)');
                return;
            }

            // 找到下一个可用的Vout编号（1-4范围内）
            let newOutputNumber = 1;
            for (let i = 1; i <= 4; i++) {
                if (!existingVoutNumbers.includes(i)) {
                    newOutputNumber = i;
                    break;
                }
            }

            // 如果编号超出范围，不允许添加
            if (newOutputNumber > 4) {
                alert('Vout编号超出范围(1-4)');
                log('❌ 添加失败：编号超出范围');
                return;
            }

            // 添加到现有编号数组
            existingVoutNumbers.push(newOutputNumber);
            existingVoutNumbers.sort((a, b) => a - b); // 排序

            // 根据Vout编号设置对应的placeholder
            let placeholderText = '';
            switch (newOutputNumber) {
                case 1:
                    placeholderText = '单路输出';
                    break;
                case 2:
                    placeholderText = '两路输出';
                    break;
                case 3:
                    placeholderText = '三路输出';
                    break;
                case 4:
                    placeholderText = '四路输出';
                    break;
                default:
                    placeholderText = '多路输出';
            }

            // 创建新的Vout字段
            const container = document.getElementById('vout-container');
            const newField = document.createElement('div');
            newField.className = 'vout-field';
            newField.setAttribute('data-vout', newOutputNumber);

            newField.innerHTML = `
                <span class="vout-label">Vout${newOutputNumber}:</span>
                <input type="text" class="vout-input" placeholder="${placeholderText}" value="">
                <button class="btn btn-add" onclick="addVoutField()">添加</button>
                <button class="btn btn-remove" onclick="removeVoutField(${newOutputNumber})" ${newOutputNumber === 1 ? 'disabled' : ''}>删除</button>
            `;

            container.appendChild(newField);
            updatePlaceholder();
            log(`✅ 成功添加 Vout${newOutputNumber} (${placeholderText})，当前输出路数：${existingVoutNumbers.length}`);
        }

        function removeVoutField(voutNumber) {
            if (voutNumber === 1) {
                alert('Vout1是固定字段，不能删除');
                log('❌ 删除失败：Vout1是固定字段');
                return;
            }

            // 从数组中移除
            const index = existingVoutNumbers.indexOf(voutNumber);
            if (index > -1) {
                existingVoutNumbers.splice(index, 1);
            }

            // 从DOM中移除
            const fieldElement = document.querySelector(`[data-vout="${voutNumber}"]`);
            if (fieldElement) {
                fieldElement.remove();
            }

            updatePlaceholder();
            log(`✅ 成功删除 Vout${voutNumber}，当前输出路数：${existingVoutNumbers.length}`);
        }

        function resetFields() {
            // 重置为只有Vout1
            existingVoutNumbers = [1];
            
            const container = document.getElementById('vout-container');
            container.innerHTML = `
                <div class="vout-field" data-vout="1">
                    <span class="vout-label">Vout1:</span>
                    <input type="text" class="vout-input" placeholder="单路输出" value="">
                    <button class="btn btn-add" onclick="addVoutField()">添加</button>
                    <button class="btn btn-remove" onclick="removeVoutField(1)" disabled>删除</button>
                </div>
            `;
            
            updatePlaceholder();
            log('🔄 重置完成，恢复到初始状态');
        }

        // 初始化
        log('📋 测试页面初始化完成');
        log('ℹ️ 当前Vout编号：' + existingVoutNumbers.join(', '));
        updatePlaceholder();
    </script>
</body>
</html>
